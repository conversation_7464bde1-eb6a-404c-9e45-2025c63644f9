 but I think the situation has changed and now we should be covered at least from the wind surf.
 So the first vendor perspective. Am I right with <PERSON>?
 Yes, exactly. So I got replaced from them and they are also the ones that we've gotten furthest with.
 Yeah, in reverse everything else. So most important as well.
 All right. So in the chat of this meeting, I've already added a short summary based on the different answers that we have
 from <PERSON> and from the vendor. So for the wind surf, there is also linked to the Microsoft forms where you can find all the details.
 However, I allow myself to make a short summary so that it's easier for us to digest all of this information during this limited time we have in here.
 So maybe I can quickly share the screen with that summary on it.
 Just to repeat everyone. It's about an AI assisted development tool.
 It's supposed to have all the developers to improve the efficiency of the developers speed up the development process and all the things around that.
 You can see the basic new information here. So it's it's going to be based on the sauce.
 So wind surf is a sauce William correct. It's a sauce solution.
 I think that would be the best definition of it. Yes, I mean with these new AA tools.
 It's difficult to categorize them, but in this term, yes.
 Yes, and I think I haven't mentioned yet that it's coming from the compact.
 So that's that's one of the compacts initiatives.
 So besides wind surf, there are three more right William that's going to be evaluated.
 Two more. Yes, we haven't we haven't gotten as far as with wind surf though.
 That's what we had the replies from them.
 Good, so right now this particular review is just for the wind surf once we're going to have more information coming from to too many.
 We're going to call for the separate meetings, maybe one covering the two remaining or maybe one by one depending on how fast you're going to be.
 Yes, so compacts as software as a service in a very important information, the beginning from the data classification.
 It's going to be using open or internal and we want to be using any personal data.
 So this tool will be available with based on some test repository without in a real source code.
 We've got real projects of overall projects of test repository for this POV version and also we will be using test accounts.
 So no real accounts from the whole group or or any other domain.
 So that's I think very important information from the Lee.
 I haven't spotted any and at sea risks based on my evaluation, it looks good to be approved having in mind that we're going to be using open internal and no personal data for this POV phase.
 Once this wind surf going to be selected, maybe some of the other, maybe one of the two other tools will be in the unselected.
 But at least no, the next phase, if you will be if you're going to have a clarity, which tool it is, then we need to go for the full.
 No story view actually also evaluating what are the other data classification that will be used might be confidential or be strictly confidential because at the end, it's going to be used in real projects.
 If you really use cases right now is just a POV based on some test repositories and the only thing that I see that is a bit worried me is is is mentioned here that.
 From the legal compliance perspective, this wind surf does not.
 Yeah, it does not enforce from the contractual perspective.
 Compliance with the ISO or you European Union AI act.
 That's something I think to have still in mind when we'll be when when doing the final selection.
 But I think for now that shouldn't be a big deal, having in mind that it's going to be open and no personal date.
 From the security perspective, I think it's it's it's quite well secured a haven't seen and the risk in here.
 So that's all based on the AI questioner, including security specific questions that were defined by Prussian and the security security engineers from from his team.
 In this case is qualified for the North Star light review having in mind that's going to be no sweetie confidential, no confidential, no personal data.
 We should be able to make this approval within this round that is the that is the assumption that is the aim actually for all of this horizontal based solutions.
 So that's like a short introduction from from my end. So I think now we should be able to open a discussion questions to William.
 Are there any questions any concerns?
 Yes, Mars.
 Yeah, I was going through the document shared on the meeting request, I should say.
 I was trying to actually get to a diagram. Do we have something to look at on how how is the query take how how is the UI taking the query and.
 What is the interface how is the data process some as a as a whole to look at do you have some diagram to form you to look at somewhere.
 I can have a look if we could provide it. I have seen it before. I don't think we've saved an hour, so the windsurf has shown it to us. So I can have a look and see if we can provide something similar.
 Yeah, because really really important text is important text is all there in the in the sheet provided helpful, but some kind of a flow will really help on top of it to to stitch that to the text what you're given us right.
 Secondly, and in a few questions you said it's already using AI hub. I think the diagram will let me know on how is it using the I have. I think it's a journey I have not yet have maybe.
 In the one of the questioner.
 I think that was that was if if I remember correctly that is like the intention to try to use it whenever it will be possible, but I think for the windsurf that's not the case.
 That's that's that's going to be using the.
 Build in the land for this POV phase. Yes, yes, and we are very open and have discussed with them the possibility of connecting our own models, but as of now for the POV won't be able to do that.
 On the bright side, however, they are ensuring is that all their models are hosted within the EU and all the data still stays within the EU.
 Yes, contractually they want to go through I saw you act for third party vendors and stuff, but they do host the models themselves and keep all their data attention within the EU, even if it's not.
 Yeah, definitely not trained on it or anything, but it's just good to know that all data passes through is still within the EU.
 OK, yeah, we need to keep in mind that it's a POV coming from from compacts and we cannot spend time on the integrations if at the end we will be using it at all.
 No, the question I had because of the question and I have the screenshot in the in the chat that is the only reason I had the question because I was getting that.
 The follow up question I have is what is the end goal of this are we trying to because we already have a GitHub co pilot which is.
 You know, we have GitHub co pilot. We have also have all the GPT supporting end users and does it differentiate what is does it differentiate with the already existing offerings at Volvo.
 Yes, so Volvo GPT in my opinion is a completely different end user here and we're aiming towards developers, you know, I'm responsible for GitHub co pilot as well, which is a good tool for for broad adoption existing workflows and sort of.
 It's a lower threshold to get started when we've tried out windsurf a bit now and what we want to examine with this evaluation is where are the gaps are we up to par with the industry standard are we up to par with the market best and we do see that tools like windsurf or cursor or a lot of smattering of other a coding tools might actually fill gaps that co pilot can't do today and that's what we want to understand.
 So having this review done what is the next step do we roll out to say the pilot users or a set of users what is the intent because because the intent when I'm going is I think having this should not confuse the audience out there whether to use in what context is windsurf providing more help compared to.
 GitHub co pilot and I'm more than happy to discuss that i'm not sure if that's the topic for this meeting but I understand but for me to understand but for us to have this review done and go forward I think I was trying to dig for that.
 Yeah, but you want to add Federico or here I think I was there was be his hand here.
 Now I have the full list if you want to go there's several elements so you choose to choose so yeah so everything from from cloud 3.57 opus to the Gemini models to open a eyes once as well so GPT once all hosted in the Frankfurt data center.
 Okay, so close from on tropics they were able to get it without using this as from on tropic yeah that's what I've been told.
 So said this is the PV as well so we want to dig deeper to understand it but we've got some confirmation that they host all the models.
 That's very interesting for us because we'll be learning so I think if you can share the list and we yes we know cloud is one of the best in class for coding.
 We're a bit stuck because on tropic is a bit looking it in its own sense even if you source it in Azure so yeah it could be good learning there it's excellent.
 I think to do this kind of investigation with complex and I think it's really we could learn a lot.
 So I don't know my age but we we should try to use that also to learn on the journey I hope to be honest.
 Yeah, but we are you're right because we don't have that opened up for the journey up right now.
 If this startup has the way to overcome a bit the limitation we have seen in the past.
 That's a good partner then.
 Thanks there so Federica. Yes, I just want to say Windsor is like half a year before co pilot I would say on how they are developing the tool and everything.
 They are a list of be talking about we don't know exactly but behind the scenes they probably are quite connected to open AI, the company, the risk company.
 Maybe they have bought it or will buy it or something like that we don't know the partnership is it's in place already.
 It's it's happened a lot since we started this period just to give that context start off as the newest version startup that they have grown very quickly and rightfully so so yes that that partnership is in place now is my understanding as well.
 Yeah, so that the problem that I can see right now is that if we want to use the NAI have they don't have a really plan for it.
 William, if I understood it when I talk to Mike a lot of time.
 I think but the most important thing that I feel is that we need to be able to evaluate this quickly because there are so many tools.
 There are another tool that we probably want and I would come to you William to try to incorporate that argument code and they are like one year before Windsor right now in development and everything.
 Yes, sorry, but we have that in our backlog as well now good for you to know that we're going to look into it but then once again.
 These reviews we need to take place and we need to be quite quick in doing this right now because the market there's so many things happening and we need to understand how we can evaluate them formally in a good pace.
 I think in this group not your group will have that in the A I group what we need to take out how can we then secure different things and also say okay it's fine.
 We secure that we can't use our own internally models and stuff like that but then how can we secure that what type of question how can we test it without compromising anything.
 That is something that I think we should do something also a list on so we can quickly decide that like your principle.
 Yes and in terms of the tools that we've looked into and want to do formal POVs with Windsor without a doubt the most compliant and secure ones with their way of doing this.
 Yeah. Okay. Thank you for the recap. We'll go ahead. Yeah, I had a couple of questions around the scope of this so we are looking here at Windsor AI editor is that correct the IDE for source for code generation.
 Yeah. And are we and so in this particular review that we are looking at only windsurf but you are also evaluating other AI tools and is the then the end goal is to select one of those tools or how what is the how are we going to go about this are we going to evaluate multiple tools and then select one of them.
 Hopefully yes. So for this review now we're focusing on windsurf because that's what we've gotten the most information we need to continually update and evaluate potential tools all the time and goal is yes we want to choose another one but we want to do that just because we want to choose one they need to prove as well that there is actual value given from them.
 So and go for me and my team is always to be able to choose another tool if they are up to power and actually fill a gap that we see from GitHub co-pilot today.
 But if we are but then here if we are going to so what are we then reviewing here because if if we are going to do a comparison of these tools should don't be as a review look at that comparison and decide what is the right tool because I'm not very clear on that.
 Okay but I think Vijay before we we do this we we still need to give a green light to do this you know POV for this windsurf yeah I think this is what we are allowing or this allowing as part of this meeting yeah to simply be able to start doing this evaluation based on this using this open and no personal data.
 As simple as that. And also this evaluation of AI ID is let's say their editors why is it being done as part of campus initiative is if you don't it be like a general Volvo initiative to see what's a good source good development tool for Volvo.
 Well from from my side we wanted to reach out to a lot of great startups that are out there so it felt like a very good fit to use campus this innovative way of working with them.
 And I think at the you really are know that the best person to drive it right because you you are owning this. Yeah and organization perspective that's that's your scope your domain.
 So you may be that I hope that was clear I'm not a part of campus I'm a part of foundational products and DPO for AIS is the development so responsible for GitHub co pilot but within my scope is also part to evaluate and assess other AI tools that are available.
 So yeah and then the last thing I just wanted to mention was since this is a tool which will hold the code source code of Volvo if it is selected so then it is very very important to look at it from a security perspective to make sure that the code that always code doesn't get exposed in other places.
 And so that is I think that is the core of this. Yeah and of course we want to continue to help deeper through the valuation if we are to continue with windsurf we will do even more reviews of course but we've already gotten confirmation from them with like no data attention no training on the state of whatever it is.
 But of course we want to have more details on that as well but verbal confirmations and confirmations in the contracts that we have that they don't use any of that.
 Okay, they even they are the only ones on the market now who has been able to to keep data retention local as well so it doesn't actually leave the PC except for what is sent to the LLM and then back and deleted right away but that's a different story which we can dig deeper into but they are I think the only ones who can are actually able to do that today.
 Okay, thanks. Thanks for James, Daniela. Hello. Yes, thank you. I have two questions. Is it defined when the POV is successful or not and or then the legal compliance security privacy ports in that review?
 Partially so we of course are comparing the tools in mid different metrics and we are actually actively updating them because the tools differ quite a lot.
 But we do have some governance and compliance things that we look at from our side we would need more assistance if we were to potentially continue with one to understand it.
 And and for the periods you have and how long is the POV going and is there anyone involved and from from legal looking at this confirmation should refer to.
 So as of now we we haven't really set the date because it's been difficult to know when we can start and with summer vacations in Sweden back and forth.
 So that would depend on how this review goes. We have talked about eight weeks or similar. I think we can do it even quicker now because we have a lot of groundwork which has been done.
 I think it's important to know that when we do talk to the vendors as well to make sure that they are aligned with what we want to do and that we can get the most out of it as well.
 But if we can say go ahead then we would set the timeframe right away and I've been it's in the scope of six weeks.
 And last question is maybe a more process question for you. Where do we go? I want to know what the north star light review is and if it's defined somewhere.
 We will share that we do.
 Okay, let's continue then Alexander.
 Yeah, I just wanted to make a quick comment about the campus. I'm from campus me and Lucas and we're here to sort of support and help the process.
 There was a question about the legal support as well. We have Peter Wilson from it's my TV legal. He's there to support us.
 We also have GTT legal involved as well as we as per usual. I should say in terms of campus. So they are along the road as well.
 And coming back to the questions by Vijay and why using campus toolbox and I'm not here to sort of market campus in any way.
 But the idea here is that we should utilize or we wanted to utilize the framework and the process as we have to sort of get things started.
 So to say so we have we have a governance structure in terms of presenting in a cross functional way into all of all the groups.
 So we have from every TDA TDBA there and we also have the standard standardized contracts and and everything as such.
 So we wanted to utilize that and it's a way to get things started. I should say and do things in a quick manner.
 So that's that's why and we could as Federico said, if there's more tools to to evaluate this can be used for that as well.
 If there is is an effort of course.
 Excellent. It's more an information, you know, like I wanted to pass on. There is an initiative going on led by Marianne.
 Now which is looking into the digital and the operating model, you know, tooling for digital and like the operating model and one of the ambition is that we improve a current way of working.
 And use, you know, like AI and all so that the real value can be derived because here like we are talking about product development right.
 So in the way, Marianne is looking into the complete IT authority, value chain starting from strategy to portfolio.
 Then after requirement to deploy you know request to fulfill and detect the correct. So the complete value chain is being looked into with an investigation going on as to whether we can leverage our existing tools optimally or whether we go for new tools and how we can improve the total value chain.
 So these inputs, you know, it will be good if it can go to her and also, you know, like when we look into such products, you know, like how we can interact with the rest of the tools we use so that you know just just by accelerating development, you know, like we'll not.
 The other things also has to fall in place, right, you know, like because then there is an IT SM change management everything has to be accelerated to derive the value.
 So, Marianne is the she's heading the architecture technology architecture transformation, so you know, like I think it will be good if you can elaborate.
 Yes, of course, and that sounds very similar to a lot of initiatives and scopes that we have within traditional products and particular deaf circles today, although our scope is of course limited to the software development lifecycle and how to boost that and make that more efficient across everything and automate that.
 But definitely there will be some synergies there that we can utilize yes, yes, that is also part of this value chain, but they are looking right from the planning, you know, again, yeah, alignment with the strategic objectives to the complete life seconds.
 I just wanted to keep you informed and that's one might sound like marketing for this particular tool, but we service also very good for stuff like that automating and getting planning.
 And so on for developers and product teams or stable teams, as we call them, but involve more efficiently as well.
 Just a point is also I don't think that the development tools is what Marion will support because I feel that the thing that William and Campex needs to do is to
 have a lifecycle for these tools because they are exploding right now and we need to be able to abort them so quickly, but they need to interact of course with the other tools as well.
 Yeah, exactly somewhere they need to connect although our scope is limited to our part, but that doesn't mean that we are siloed and doing it by ourselves, there's always needs to be interchangeable stuff there.
 Yes, I that was my attention in like how we can avoid that silo.
 And you know in the next right now she's focusing on strategic to put for you, but the next thing will be a requirement to deploy and then this will come in her scope also.
 And just alignment and discussing with each other and making sure that we are transparent and knowing what's happening is probably good enough and then we can find where where we can help each other.
 Yes.
 Alright, so I think it's time for the voting.
 So don't hesitate to rise your virtual hand if you're okay with kicking off this POB with windsurf using the.
 One last question guys, so I'll take in just 10 seconds.
 Let me follow up review the moment we have the results of this POB so as to go ahead and proceed what we want to do on a global on a bigger level at Volvo.
 Yes, in this that's the function.
 Exactly, so we take a decision now that it's okay for the PC and then there will come back in results and everything.
 So thank you for voting it's officially approved. Thank you so much guys and we are within the time so well done.
 Very efficient.
 Thank you so much.
 Thank you everyone.
