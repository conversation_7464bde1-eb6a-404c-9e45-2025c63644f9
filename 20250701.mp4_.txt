 - Hello. - No worries.
 Welcome.
 Is it hot in Poland?
 Is it hot in Poland, huh?
 - Hello. - Good morning.
 Sorry, asking if it's hot in Poland, yeah.
 Indeed, it's very hot.
 (laughing)
 - It's hot.
 - I know that, yeah.
 - Hello, hello.
 - Hello, everyone.
 - Hello.
 - How many degrees do you have in Poland?
 - Currently, it's 21, but it's still quite earlier,
 so I think it will get onto around 30 today,
 based on the forecast, so.
 - Yeah.
 - It's really summer here.
 - Our team in India is like,
 what are you complaining about?
 - 21 and half.
 (laughing)
 - 21 is not summer, guys.
 21, you want to wait for it?
 - Exactly.
 (laughing)
 - I have 31 here in Valencia in Spain right now.
 So.
 - Oh, okay.
 - You're in Spain.
 - Yeah, yeah, yeah.
 - All right, let's see.
 Welcome, everyone, to the AI Working Group,
 our internal one for the architecture
 and capabilities work stream.
 I see a few faces that I haven't met before,
 which is super nice.
 (speaking in foreign language)
 <PERSON>, I think as well.
 Hi, guys.
 - Hello. - Hi.
 You want to introduce yourselves?
 Welcome.
 - Of course, we're sitting in the same room,
 but we had some technical issues.
 So now we're on different laptops,
 but from my sound.
 But nice to meet you.
 I'm <PERSON><PERSON>, and both the (speaking in foreign language)
 We have all joined the Pennsylvania Security Stream.
 So for those of you who've met <PERSON> previously,
 I'm entering his role as a project lead,
 as he's off to parental leave.
 And me, <PERSON>vian <PERSON>stoffer, we're off from Cape Gemini event,
 so we're external.
 Very happy to join.
 We're in a bit of a transition period now
 with the previous consultants starting to onboard us
 and starting to off-board themselves.
 So hopefully we will be up to speed,
 at least shortly after vacation is our ambition,
 but very happy to be here.
 So we're working very close to <PERSON> <PERSON>fei, then.
 - <PERSON>, warm welcome.
 <PERSON> to have you onboard.
 So basically, yeah, you just need to jump in
 and join the train and everything will be sorted out.
 But the working group we have here is our,
 for the architecture and capabilities work stream
 that is then another horizontal work stream.
 Basically, we call it sometimes the technology track.
 And we're basically in this call this week,
 aligning between all the different initiatives
 in our work stream.
 And what we have said with Daniel is that it's really good
 that she engages in this work stream,
 especially when we have Wednesdays on Fridays,
 we have architecture committee
 and we invite like ongoing use cases into this constellation.
 And that's where we, of course,
 have a legal and compliance and security perspective
 in terms of review, making sure we're doing the right things.
 So here is this intersection between our two work streams
 are super important.
 So we will have much more conversation around that, of course,
 but to just try to find that connection we are working.
 Oh, so with that, first of any.
 So I have a couple of topics to update on as usual.
 Is anyone else have a topic on their mind
 that they want to add to the agenda?
 We can start there.
 I already have on the agenda a bit of update
 on ongoing topics, which is the ITX strategy
 and the capability map that we take.
 So that is already on the agenda.
 But anyone else that has something to add just raise your hand.
 And it's also fine if you add it throughout the meeting.
 - I would have one topic, but unfortunately,
 I need to leave half past 10
 because we have overlap in dump hole meeting
 in digital technologies.
 And this is about the valid data framework,
 which is like all in one AI platform
 ensuring compliance with the AI act and other regulations.
 So there is one nice platform that was, you know,
 send it over to me from Cassia Carlson,
 so Innovation Manager, which she has seen in Paris.
 We've attacked Paris.
 So I think that's one the topic I would like to discuss,
 whether we would need such a solution,
 not necessarily this particular one,
 but if we've been considering anything, you know,
 for the newest feature like that, yeah?
 So that's just one.
 - That's great.
 Let's, okay, I'll be fairly quick on my update
 and then we'll try to take that topic 10.20 then.
 But please flag it, so if I forget about five.
 Yeah.
 All right, let's kind of share.
 So a couple of things on the agenda,
 but the first thing I'm gonna update on is that
 we have a staffing update on myself.
 So I'm actually gonna, I've talked to some of you about this,
 but I'm actually gonna take another job myself,
 meaning that I will reel off my role
 as a workstream lead in this workstream.
 And I'm, yeah, I can share more information about that,
 but I'm going back to StartAppland,
 which is probably a really bad idea.
 But that's what I'm gonna do.
 Meaning that I will leave EY and then leave my assignment here.
 Volvo.
 So I'm currently spending a lot of time together
 with the charlion sub, of course, and EEPAM
 to trying to find the new person that will take on.
 And we have really good conversations.
 And it's, from that perspective,
 there's also a good, good timing.
 Since we have this ramp up and relaunch now,
 dark after, after summers.
 So I'm working close with EEPAM to make sure that they get in
 and also put their perspective and plan in place together with me.
 So everything will be planned accordingly going forward.
 I'm sure it's gonna be great, but I will miss all of you
 because I have enjoyed this assignment a lot myself.
 So that's update number one.
 And of course, there are a lot of activities
 ongoing on that to secure a new lead.
 So that's that.
 And then I'm gonna continue with, let's see here,
 staffing update.
 And so another staffing update is then that we have good progress.
 Now I'm gonna show it this slide.
 So we have good progress in staffing now.
 Everything is taking a bit longer than that we want to us as usual,
 but just to give an update.
 So the new lead role, that's the one I just said.
 We have good candidates in the process.
 Hopefully we can agree with them already next week to continue.
 And we have also ongoing discussions with EEPAM, of course,
 on the entire connection also to the strategy work.
 How the execution plan can look like going forward.
 Then within AI capability realization,
 we have secured three solution architects with the amazing help
 of Mahesh, Stine, Tobjön, and Christoph that has interviewed
 and met these candidates.
 I just got words that like Pavel, who's on that list,
 cannot join until August.
 So we can take that offline, but nonetheless,
 we have three secured with a bit of a question mark on Pavel.
 And we have a really good candidate for AI capability portfolio
 manager that is meeting with Charlotte tomorrow.
 So hopefully we can close that one.
 So if we get green like that, then we have the entire capability
 realization team, like the first version of that team,
 to be able to kick off.
 We also have a business change manager and communication
 resource.
 It's partly down here, but it's also really much
 on the work stream level to be able to work
 close with the people in the skills work stream
 to make sure that we have proper communication towards
 everything we do, first like adoption of all the capabilities,
 but also in architecture governance.
 Like how do we reach out?
 How do we make sure that we have the right communication plan
 and reach out to all the different parts of the organization?
 We're going to have the resource joining there
 is Johanna, who has been working within the data work
 for quite some time.
 She's a consultant and she will be on board
 at now into the work stream.
 That's good news.
 We also already have people join like Nils and Nils,
 what's the name of your colleague?
 Sorry, I lost his name.
 Anyway, what's his name?
 Again.
 [INAUDIBLE]
 Thank you.
 Thank you.
 Has also joined our work stream from VisualDit, which
 is great.
 That's green light there.
 And other update is that we have secured
 on the AI tech strategy lead with SODIF.
 It's also in this call, I think.
 I forwarded the invite today, so maybe that was a bit late.
 And we will have a bit of an update on the ongoing topics
 there what we're doing there.
 And as well that we have onboarded two vulnerable resources
 to lead the subtracks of the AI tech strategy lead.
 So that's really, really good progress.
 And we're also having conversations
 to secure the Volvo lead enterprise architect.
 And we're most likely going to have a lead solution architect
 as well.
 So that currently working on that.
 And hopefully we'll close that in the coming week as well.
 So all in all, taking maybe two or a week,
 then I wanted to, as always.
 But I think we have a really good chance
 to close all of these within this week or the next one.
 So we're ready when we sort of relaunch and kickstart
 after summer vacations.
 And summer vacations for the majority of us.
 I know that there are about four of you
 that is working the entire summer.
 That's on staffing.
 And again, when we go back here, because I'll come back
 to the summer plans, because I just
 wanted to connect it to the hybrid relaunch planning event.
 We're going to have in week 34.
 So we will have this as a hybrid event.
 We can unfortunately not have everyone to travel.
 I try to get the whole time to travel, but we're not allowed.
 So I will ping the ones of you that I want to be in Gothenburg.
 But it will be a hybrid sales remote event for week 34.
 And that's also where the new work stream lead
 and all the new lead resources from E-PAM as well as BCG.
 And if we have other resources onboard it,
 it will be a bit of a kickstart to onboard all the consultants
 and meet the team and make sure that we have a proper planning
 together.
 So if you feel that things are a bit unclear now
 before vacations, everything will be sorted out
 when you in week 34.
 We will do a lot of work during summer in some of these areas
 as well.
 But that's where we do the proper kickoff together
 to make sure that we have a coherent plan
 so that all the meeting invites will be updated as well.
 And me and myself, I'm here two more weeks
 before I go on vacation.
 But then I'm here.
 I come back this week 33.
 And I'm here for two weeks.
 So that is the plan going forward.
 And I will be part of this event as well then.
 So that's that.
 And the summer plans coming into that.
 So we are given that there is--
 if you're interested to see vacation plans,
 that's that file I shared with all of you.
 So there's this summer vacation plan.
 And we will cancel this occurrence of the work group
 between 29 and 31, also the Friday ones.
 However, we will still-- VJ will still be
 running the AAC on a new basis.
 So currently, we don't have any topics.
 And we will ask if they can wait to week 33.
 But if that's not possible, we will have AAC running during the summer
 to make sure that we're not blocking any initiatives.
 But the AAC will be canceled 29 to 31.
 And the nails will run the work group week 32.
 And I'm still waiting.
 But I'm back 33.
 So that was my update on staffing.
 Yeah, that's that.
 Any questions on this?
 We're super clear.
 We're still clear.
 Good.
 Then did someone say something?
 No.
 I just heard--
 No, it's not, it's not a university.
 We have a lot of people in the room.
 Oh, there, Daniel.
 When will you leave Catalina?
 Was there a time?
 Yeah.
 So my final date is August 22, like the Friday in week 34.
 And I've been working with Charlie and Seb and Ivan
 for a number of weeks now to secure this.
 So it's not that they didn't know until now.
 We have had a conversation for a long time.
 So but I'm super sad to leave all of you guys.
 Maybe I'll come back one day to Volvo instead.
 Who knows?
 But OK, so that's that.
 And we're in 10-sick.
 And maybe it's good to switch over to your topic, Arthur,
 as we have enough time.
 And then we can do a bit of an update
 on the ongoing initiatives we have.
 All right, thank you so much.
 So as I just started, I've been approached
 like has your cross-innovation manager
 about interesting solution which is seen in Viva Tech Paris.
 It's called Validator.
 It's a one, only one platform, which
 is about to support the, say, whole compliance
 around AI compliance with the AI European AI Act.
 And also, AI standards.
 So, but it's not really about this particular solution.
 So it has just triggered me to start thinking whether we
 would need something similar or not.
 Of course, if we would say yes, then most probably
 we need to do a much broader research,
 not just jump on one particular solution,
 which was identify in this Paris Viva Tech.
 Most probably you would need to look into the Gartner
 recommendations and ask for RFI, RFPs,
 and so on, do the whole research.
 And then Sasco's onboarding.
 But this is just about to start thinking whether we would
 need and we would, if you find it interesting, first of all.
 And if he has them, I think we can start thinking how
 to actually onboard such a solution.
 But in a much broader, say, man, they're not only jumping
 on one solution that was identified.
 So open question, what do you think?
 I think this would be beneficial for all the legal compliance
 on also for us when we're doing all the different assessments.
 What is your impression?
 Do you think maybe we have already something like that in place.
 And I'm not aware of.
 So I just wanted to double check here.
 Yes.
 Yeah, curious.
 Because there is an ongoing, like, tool track
 and legal and compliance.
 So how does that relate to this validator tool?
 And also curious.
 Because I know that there may be Vina has a comment.
 Because I was curious on, like, Mats and Vina's comment
 on a tooling, et cetera.
 Yes, that when I was about to tell the same thing,
 there is activity going on within the legal and compliance
 stream, trying to find a governance tool.
 And then for the presentation, I thought,
 I don't know whether you missed it.
 There was a presentation recently from Vincent Oscar
 regarding some of the tools that they identified
 or they are doing a research on.
 So the requirements were gathered.
 And I had shared the requirements list to this forum,
 members of this forum.
 But maybe this will be an addition to whatever you said,
 the Vantator tool will be an addition,
 if not already identified to that list.
 OK, so that is basically that is already ongoing.
 And that's totally fine.
 So this is what I wanted to ensure.
 So maybe I will get back to you, Vina,
 directly, and asking about the requirements
 and also the tools which are in the scope.
 I think it would be still worth to verify whether this validator
 is part of it.
 If not, maybe it still makes sense to add it into the scope
 and compare it with others.
 Thank you.
 Can you ask a few?
 Yeah, on that note, because I think it would be interesting also.
 I mean, connect with the mixture that
 was 100% aligned with Lincoln Land Compliance track on this.
 But also, I mean, we could naturally
 would be good if we test a couple of tools within our process.
 So I mean, it could also be that we run this as a proof
 of concept to test it out, because that's
 also a good way to decide on tools.
 So just so we don't abandon this just
 because there is an ongoing initiative.
 The important thing is we're aligned
 and that we're not running separate tracks.
 But maybe this could be a way for us to test this
 in the architecture committee.
 Yeah, definitely.
 So the reason why I'm coming here
 is I want to ensure that we do it as a one investigation.
 One broader investigation.
 Not multiple disconnected investigations.
 So, yeah, good.
 Good question to Daniel, since you also
 have a number of people ramping up.
 And we got an introduction in the beginning of this meeting.
 So that's great.
 But who is going to take on the work that Joseph and Vincent
 has been running for the tooling?
 Is that going to be handed over?
 We have not decided tooling part yet.
 So it will be fire ill far going to one
 of the captain and our consultants.
 But I think it's really important to be very close
 with the streams, because there are so many different requirements.
 There are requirements coming from your site and requirements
 from our site and maybe from other sites
 that need to be captured in a tool.
 So we cannot drive this by ourselves.
 No, I was more thinking on, who should
 we make sure to align with?
 Is it you, Ilva, Dan?
 Yeah, as a start, I would say.
 Yeah, it's a combination of art or Ilva and Vina, I would say,
 for maybe a follow-up conversation.
 So we align all the different perspectives here, maybe.
 Sounds good to me.
 Yes.
 Thank you.
 Yeah.
 Perfect.
 Cool.
 Thanks, Arthur.
 And on the topic of AIC, because VJ, we usually
 do an update on initiatives, but I don't think we have anything.
 But do we have any topics?
 Yeah, this week we don't have anything.
 There was one sales force related review scheduled,
 but I think it might get postponed there,
 waiting for the contract to be updated.
 And I haven't done that yet, I think.
 OK.
 So we have asked them to update it before they come for the review.
 Yeah, but just a comment here, because we still
 might get one for tomorrow.
 That's something I'm still discussing.
 It's coming from campus.
 And the question is, if we still allow for this review this week,
 or we still want all we want to postpone it as we discuss VJ.
 So if we want to postpone it, I think that's totally fine.
 I can arrange that.
 But if there is still a chance--
 If there is an urgency, we can take it.
 It's not a--
 It's not super-agent, but the question
 if we want to do it still this week.
 And who is available, right?
 Because--
 I think preferably, if it's a week 33 or later,
 it's better to say that there are enough people involved
 in the review.
 We don't want just a couple of people reviewing the whole thing.
 All right, then now we arrange that this way.
 Just make sure that it's not urgent,
 because what we have decided, very important,
 is that we shouldn't be a bottleneck.
 So just so we don't push just for the sake of pushing.
 So it's fine if it's not urgent.
 But we just need to understand the consequence of that.
 From their perspective.
 Yeah, of course.
 OK, good.
 And Matt, yes.
 It was the first to wonder, VJ, about the contract.
 Is that coming from digital delivery?
 That statement?
 Because the stakeholder here, Volvo Tracks,
 they are really pushing to get this up for a decision.
 Yeah.
 So currently, the process that we have
 is that unless the legal and compliance
 has approved it, we are not moving it,
 because the AI parts of it in the contract
 needs to be updated to what Volvo expects it to be before the--
 And anyway, they will not be able to deploy anything
 unless the contract is updated.
 No, Matt, I think-- and maybe we have missed this.
 But I think we are not there yet.
 We first of all, it's a decision of what
 they are allowed to do with at all.
 And that's the AI, that should be the judge
 of the governance of that.
 So it's not given, actually.
 It's not obvious.
 It's a matter of principle.
 Federico, you know very well about this one.
 So if we are going to take a review only
 the implementation part of it, I think
 that we may miss the right decision, actually.
 The main decision.
 The main decision, what do you mean, there--
 But are they allowed to use it at all?
 Right now, the strategy is not defined yet.
 That is one thing.
 And then, however, we do get that the legal part needs
 to also be investigating that.
 And I understand what you mean, Matt.
 And I know the urgency from them as well.
 But are they driving it as a park, or are they
 driving it up your implementation?
 Yes, I can't say it by 100%.
 Actually, I think it's an implementation.
 Daniela.
 Yes.
 Sorry, maybe I missed some things.
 But can we be a bit more explicit what exactly the issue is?
 So that from the legal perspective,
 it might be able to help out.
 So Salesforce has come with--
 the team has come with the request for review
 for enabling the AI part of the within Salesforce.
 And there is a contract from a legal perspective.
 There are multiple things that we need
 to look at as an AI architecture committee.
 Of course, from the architecture perspective,
 whether it is allowed, and they should
 go ahead with it or not, like Matt mentioned.
 Then there is, of course, other things
 that also that we check, like the security evaluation
 has been done.
 And the legal and compliance check has been--
 legal contract update has been done and so on.
 So currently, the legal part has not been--
 it is still-- contract is still being worked upon.
 So even if we are OK with the from an architecture perspective,
 that we still need to evaluate.
 But assuming that even if we are OK with the architecture
 perspective, unless the legal and compliance check
 is done, we won't be able to fully approve it.
 So that's why what we said was, you
 finished the legal part, and then we will call for a review.
 I think this is the one that contacted me for a spread
 last week and go to production before we'll go to agency
 before they finalized the legal negotiation.
 And then they said that they needed to have it finalized
 before they haven't approved it for the next step.
 That was my recommendation.
 I don't really see how we can approve anything,
 or if it is, we can approve anything without having that in place.
 I am in either legal or not, either not.
 Yes, but I see it a little bit differently,
 because if we don't allow Salesforce Agent Force
 to be used in Salesforce, it's a principle
 that the AI is issued.
 You judge, then there's no idea to actually
 do any other types of reviews.
 So from a principle perspective,
 we need that decision first.
 Is it OK to use the built-in AI capabilities
 in tools like Salesforce?
 They will become in Dynamics, it will become an SAP.
 It will come in Microsoft, in the service now as well.
 If the AI says no to that at the moment,
 then it's no-- it's ways to do everything else.
 Then they should have installed much, much earlier, right?
 It has been going on for a while, actually.
 So we have to-- we miss out the alignment here.
 Yeah.
 And Federico and Vijay, I think you said that.
 And where did we start with the architecture review, then?
 Like, what's the--
 What's the reasoning there?
 So we have gone for the review tomorrow, actually.
 So we haven't reviewed it yet.
 And you will keep that on, or?
 Yes.
 Yes.
 But OK, can I request that you take the question
 of the principle here?
 Do you grant-- if-- later on, of course,
 I understand that they need to go through the different steps.
 But from a tool perspective, do you
 grant the usage of Agent Force in Salesforce?
 Yes or no?
 OK.
 We will-- we can have that tomorrow's discussion.
 OK.
 Cool.
 Yeah, and because I foresee that that has gone through
 the AI questionnaire as well.
 And that we have done a proper analysis into that.
 So we just make sure that we make a well-grounded decision
 tomorrow.
 Yeah.
 We know--
 Yes.
 But since Salesforce SAP and on are used
 for critical business capabilities,
 and by multiple TVAs today, should in that we brought
 to the HEC tours for our discussion,
 at least they can discuss on the business vision
 and business trust that they see.
 And in the AIAC, we can just look into what
 could be the risk that would be introduced
 because of enabling those speakers.
 The AI topic is delegated to AIAC.
 So the first decision or recommendation
 need to be done in AIAC.
 Yeah, the business will be--
 Yeah.
 Then we follow the ordinary.
 The business will you will follow the ordinary process about--
 Yes.
 So AIAC and HEC when needed.
 Yes, nice.
 So in the TVBAC, they might have discussed this.
 It should be-- since it's a foundation product,
 it could be able to HEC so that they
 talked about the business vision that they have
 for using agents force.
 And then they can be directed to the AIAC
 so that we can look into others.
 We actually go via AIAC first.
 The overall, this is part of a big initiative.
 And now I don't remember by heart,
 but I'm quite sure it's been an agency once.
 When I started with this project with--
 because this is one part of the whole initiative.
 It's lead generation.
 Yeah, it's a guide itself.
 It's a guide itself from Balboa Truck,
 give me to say--
 Right, it's nice.
 Yeah, I think so as well.
 Yeah.
 And that's what's up there very recently.
 But we don't have any kind of a guideline from AI
 is such perspective, I mean.
 Because the task is talking to their data cloud, actually.
 So do we see any risk, anyone?
 Because they will talk to their customer opportunity,
 which is costly in their type of agent force.
 So I don't see any risk there.
 If we-- it's already in their data cloud.
 I mean, all of our data is customer data.
 Opportunity data is in Salesforce cloud already.
 And the agent force will use that data to--
 OK.
 Sorry, context.
 I mean, but we don't have any standard
 guidelines for agent force or SAP, you know, those things.
 Correct.
 Before you, Daniel, as you just say.
 So right now, the process moralizes.
 We evaluate the AI part.
 See how they're using AI, the models, and everything.
 And do a secure--
 from that point, from an AI solution part,
 from the architecture part.
 So one can say, right now, we more or less approve most of them.
 I don't say we do everyone, but we have one about legal,
 where they send out data and everything.
 And we said, if legal and compliance say yes to that,
 then we can approve it as well.
 But we don't say it's the solution.
 But then we have stated that as well.
 So Matt, just to say, right now, we
 don't want to stop anything.
 And that correct me, Catalina.
 That is what we had on Diana.
 Yeah, but not really, because the best suited
 to create those kind of principles should be this group.
 So if we have reasons that we want to flag something,
 or we want-- like, we have doubts.
 We should articulate that.
 And then, be very clear on what basis do we say no?
 Then that is actually a reason to bring it to edgycy,
 if there is an escalation.
 Because we say no to something that has been pushed.
 But we cannot just assume that we say yes to everything,
 just because we don't have the principles.
 Because that's what we started with a questionnaire.
 And everything is also to set a bit of a principles.
 And that's part of the process as well.
 You write that we haven't done the larger strategy work,
 but it's not an argument for us to just let things through.
 So there is a middle way there between where we incrementally
 create those kind of guidelines as well.
 That will, of course, take off even more now after some
 years.
 But it's like, we need to have that in the back of our heads
 and continue to update those kind of lights as well.
 And if we feel that we don't have enough understanding,
 or we don't have enough guidelines to be
 able to take that kind of decision, we
 don't need to flag that.
 And we can say it's due to x-reasons.
 But we cannot just assume that we're
 letting everything through because of that.
 Because then that will mean that everyone can start to use
 agents for us, for example.
 If we find that to be a risk, we cannot let it through.
 But if that's the case, I'm very happy to know that in that case,
 because then I probably need to take some additional action
 to push that.
 So we get to clarify that.
 So we just need to be very clear on why we're taking decisions.
 I can what the assumptions we're making
 if we're taking a yes or no decision.
 What the-- sorry, on what basis?
 But agent force is an internal architecture of Salesforce
 and just like other Salesforce features.
 And we buy Salesforce as a SaaS product, as a package.
 So are we allowed to go into deep and check agent course
 internal architecture?
 I mean, that will not give anything for us, actually.
 Instead, I think if we have a proper compliance guideline,
 already available from the security, then we are OK.
 Because we have our own model, like we have our select
 and model like DPD.
 But agent force will not use DPD.
 Agent force can use their model like Einstein or something.
 So isn't it like we should first check the compliance
 in just like we have a taskcord guideline,
 similarly taskcord AI guideline.
 And then if they approve, then we are good for me.
 So I don't see that to stop agent force anyway.
 I would tend to agree with the cash, because it's the business
 access service.
 If there is no legal implications,
 I think we will not be able to say a no, as everyone
 has said, without any properties.
 And because we have taken the risk to host our data in the cloud,
 say it's cloud, that's the biggest friendship.
 And then using an embedded AI was part of the strategy
 from the beginning.
 So my point is, I'm not saying, like, yes or no,
 I'm just saying that we need to have a well-grounded decision.
 And I think we're getting to that.
 Ash, I think--
 I just want to add to what you said, Katina, yes.
 Of course, we need to do a deep dive.
 What important is the data?
 How is the whole data going in?
 Where is it going?
 And what is the classification of that data being
 internal open, confidential, strictly confidential?
 And compare that to the in-house tooling, and then go at it.
 [INAUDIBLE]
 Daniela, did you want to say something?
 Or you had your hand raised before?
 OK.
 I think it's a bit confusing, actually.
 Because if the decision taken, and it's given that we allow
 embedded AI, then it should be written somewhere.
 At least I am not aware.
 I cannot act on this at the moment.
 I feel like I can't.
 So if we want to communicate something, and it's given,
 then it should be documented, I think.
 That's in the initial slide that was presented.
 And my Haitian team, you can probably add--
 there were the various AI architects mentioned.
 And embedded AI was very much part of that.
 So-- and that was part of the discussions right
 from the beginning, yes, like where all can be.
 And part of their use before I, before we installed.
 Which line are you referring to being enough?
 The initials-- I have to search for it, Matt.
 But the initials, I think that my Haitian team,
 the web presenting, my Haitian team,
 do you remember those architects, which
 were being mentioned in AI architects, embedded AI?
 The Cords AI Archetype, we know.
 Embedded AI and--
 Yeah, we store it within that.
 But then we change it to Cords AI.
 Oh, C.I.
 OK, OK, yeah.
 It was part of the presentation from the beginning, Matt.
 Yeah.
 And someone directs me to it, or?
 Yeah, I guess so.
 Yeah, we now end and the team.
 Do we have guidelines, directives on Cords AI?
 Because we are still only with the two Archetypes,
 which is analytical and genie.
 I think the Cords AI was a fourth one.
 But if at all I'm missing that part,
 when we have specific guidelines on Cords AI,
 maybe you can forward, we know, but I'm not sure.
 I've not seen one.
 And there was something created in the past.
 Yeah, I'm not aware either.
 So to just summarize, it's like this.
 I would say that we can probably take a decision,
 and we will do it grounded on the AI part,
 how it's implemented, how it works.
 Right now, it can be changed tomorrow
 when the new strategy team comes together.
 But we can only take decisions.
 So we can't write it in stone right now.
 It's just like what we know,
 and what we can take decisions on today.
 And that is what we will talk to them tomorrow as well.
 When we listen to the solution, we see how it implemented.
 We have a lot of people like Yvahid, Mahesh and Stain,
 and a lot of people that we'll ask about.
 And also also, he's not here as well,
 to ask about the AI solution part.
 And from that point, we can say what our recommendation is.
 And right now, as everyone says,
 they are already approved,
 but the security part of the AI implementation.
 We don't know, and then maybe someone else at Volvo
 from the security people say we don't allow that,
 or legal say that we don't allow that.
 Because we don't need to know, are they trained on our models,
 our data, what are we using with the data,
 and all those questions that we haven't answered yesterday.
 But for me, it's like what everyone says.
 If we already have the application approved and secured before,
 we only need to secure the AI part as well.
 And I don't see that should be a big problem.
 But let's see.
 I just started checking, because we say,
 we have this legal check that needs to be made.
 Daniela and your team, do you feel aligned with this?
 Do you know what kind of decision three they're going through?
 When we say that?
 To be very transparent, I'm struggling here,
 because we, there is a contractual
 negotiate ship ongoing by the GDP legal council,
 so how I understood from Helena.
 I'm not sure if they get any input from the business.
 So they, they tried their probably their best to embed the best checks
 in the contract, like you should not train on the data
 and the data should be in Europe or whatever these checks are.
 But then related to the usage of that AI,
 there might be different use cases in the end
 that it might not be suited for the use it or not in the business.
 So I'm not sure if that's where Maths is coming from.
 But I don't think you should say, yes, start using it.
 If the contractual relationship is not sorted out yet,
 or if it's clear for people like Maths,
 what is the status of that?
 I think that should be very transparent.
 There should be somewhere, you know,
 a sales force contractual negation still ongoing,
 expected end time two weeks or very clear for the business
 that we have not captured in the contract yet,
 there is training on data or not.
 Because otherwise, you have lots of parallel processes
 where checks need to be done and you need to have these green flags
 up, but if you have a green flag here and a red there,
 then you still cannot, as a user, be confident in using it.
 But if we are not transparent, then you don't know.
 So it's a bit blurry, I think, from the user perspective.
 It's for sure blurry.
 However, we actually act as the final gate here
 when the AI is in them, since we're not saying OK
 until the legal and procurement has a green flag.
 That's the way we interpret this then.
 If you start to use it, then try to negotiate your sort of,
 you have no leverage left, right?
 So then you will not be, then you accept that term.
 I think we should be a go-no-go, maybe before we even
 start to negotiate those terms.
 The actual use case should probably be approved first.
 Do we want to implement this or not?
 And then you start to look through security if it's possible.
 And the legal terms if it is possible could be
 and no go from either security or from the contraction terms as well.
 And that's actually what I am saying here.
 And also is that the answer that we'll track is requesting
 as a first step, then they know that we need to go through
 the appropriate, no-store review.
 Topics and both legal compliance, etc, and security, etc.
 That I'm sure they know that they don't know if I allowed to use it at all.
 But it's a bit like when we start the sourcing match
 that we have this pre-decision from, yeah.
 So maybe that is obviously it's not clear.
 I think in this process that we need to have that sort of way of work here as well.
 Or maybe it was not clear for me, only, I don't know.
 But we jump to Drico Navater's not here.
 I think from an AAC perspective, we need to make it
 clear what that process looks like.
 So it's also clear for everyone come, not only for this team,
 but it's clear, but it's also clear for the initiatives that is brought.
 Are you taking an action based on this conversation to the make sure
 that that gets incorporated and it's clear and then come back to this group
 to give an update on that.
 Let's do that.
 And I know that they always verify that this security and legal compliance
 are checked off before we say our thing.
 That is what we normally do.
 Correct me if I'm wrong, we get that's correct.
 So we are, and that is the normal and most of our review process
 that they need to sign off before we can sign off.
 But we will document it better and have that in the list as well.
 But I think that is still valid though, right?
 It's just that it's not makes no sense to start the security review
 and the requirement review before we have a first initial go.
 I think that's what we are trying to say, right?
 So because we will just waste the people's time
 if we try to implement something that would later not be suitable
 for us to implement, right?
 But who should say the first go or no go?
 Yo, in AJA C, that's the question I have lost.
 OK, but we are no one else.
 If you have this code guideline or principle that you refer to,
 if that, if everyone knows about that except for me, maybe,
 then you base the decision on, OK, we have a principle here
 and here we have one new tool.
 OK, it's a line with the principle go.
 First, check on that.
 If I get that in written, then you are passed the first step.
 There are no one.
 I can't do this in agency.
 You are the one that have the knowledge around the AI
 and where we want to go.
 That we can't override an agency.
 OK, but then how can we then secure what Catalina says
 that we are the stopper in the end?
 So secure that everyone have done the security
 and legal compliance.
 Because we can only do a recommendation.
 They can come with three different AI solutions
 and we can say, all three is OK.
 Is it a cut solution?
 And then we can also say, OK, if you're sending data
 and when you're handling the data correctly and everything,
 we can sign off on that as well.
 But still, there can be a risk when you're sending data.
 And that risk is security and compliance
 that are signing off that.
 We can say that the solution is not the best, but it works.
 OK, I'm not sure we should continue with this here.
 But the process, the north star process
 is that you can get a go-no-go-early stage
 via the agency or in some cases the decision
 to take in the AC.
 However, the agency has delegated the AI part to AI AC.
 So you take the first go-no-go decision
 if the tool is allowed on behalf of the agency.
 And that is what I see it as well.
 We have approved the tool from the agency
 and everyone that it is used in whole world.
 The only thing that we can then sign off on is the AI part.
 And that is what I say that since we don't have a strategy
 to use our DNA AI hub or whatever we want to do,
 then we don't have that decision.
 And I don't see that when we look at the solution,
 we will almost say, yes, but I can promise that.
 Because it's not black and white.
 It's sometimes grayish of them as well.
 Even if a principle saying, OK, if the application is approved,
 then the AI solution is always approved.
 That we can't say, because we are not knowing
 how they are using the models.
 No, if you could promise that, then we don't need that.
 That we can automate the entire process.
 Trick-a-part.
 Sorry.
 Yeah, but it's this group here potentially with AC,
 but AC is more to go through the rear part of it
 that have the possibilities to know where is the reaction.
 How do we want to evolve the AI landscape?
 How do we want to evolve the capabilities?
 There are no one else that will say anything about that.
 It's the program and the team here.
 I think that must be clear.
 And I guess you agree on that chapter right now.
 Yes, 100%.
 It's 100%.
 So then a bit of a comment to that.
 We know that we're not 100% staff to do this 360.
 So we do it best effort with the existing principles we have.
 We know that we need to evolve them over time.
 But nevertheless, the decision needs to be very clear
 on what basis we took the decision and what assumptions we made
 based on that decision.
 And that decision is, of course, based on your assumption
 where you think this will go in the future.
 No one else can judge that better than you.
 If everything will be a new hub, a new hub, eventually,
 then you know that if it will be a mix of that
 and built in embedded in course, then you know that I'm sure.
 That's in the tech strategy work group.
 Also, this is being discussed.
 And I remember that you know, like if this is one of the pointers
 we have picked for both for identity AI and for the general AI
 strategy, that is one thing.
 But another point that I was thinking is, you know,
 we are still bound by our principles.
 So reuse before we will still hold good even if it is AI solutions.
 So that would also mean that, you know, we cannot pull out
 embedded AI.
 It doesn't take that.
 But you need to take that in consideration, I would say.
 It's not that black or white.
 It can be a good reasons for it.
 But you should always consider the reuse option.
 But as a good architecture is that maybe to do it in AI,
 take an example.
 Let's say imagine that we say that everything should be
 generated from an architecture perspective.
 That must maybe not be optimal.
 And that needs to be elevated on.
 OK.
 And Federico, do you want to summarize what we now decided?
 So we make sure that everyone is aligned.
 What I understand, we need to write some principles
 and then decide on those principles.
 But at the same time, I think we also
 need to do the process.
 Define the process in the better ways.
 So we have aligned every step.
 And let me in video and look into that and come back
 and present to this group.
 We will contact the legal compliance people as well.
 So we are aligned with them.
 I think that is the best way we can go forward right now.
 Hi.
 Great.
 And what I-- and also, did you already doing this?
 So we're documenting the decisions.
 We're taking important transparent on how we're
 taking decision that we're only to do.
 But just highlighting that importance on that again.
 May I ask a short comment here?
 I think that the step one of going,
 if we are going it with this platform or this platform, et
 cetera, need to be described.
 What does that mean in the first stage?
 It's a conditional approval until the legal compliance
 security part has been gone through.
 This maybe should be something we wish-- maybe it's given.
 But it seems that we should actually
 be explicit and document that how it works.
 Of course, the first decision cannot override.
 We'll never override the legal and compliance
 mandatory checkpoints.
 I think that's clear for everyone.
 But I agree we should make it more clear from the process
 perspective.
 But then we'll document the exact decision
 with it is conditional based on legal and compliance
 and security and whatever is spending.
 And that we have done in other cases as well, like previously.
 Daniel.
 Final comments?
 I think it should then also, and that's on our part,
 be very clear, what are then the legal and compliance
 principles that need to be in place to get it done?
 Is it the contract?
 Or is it also the AI assessments around use case or not?
 Because otherwise, it still gets blurry.
 It should be very clear on what is then that part.
 And that's absolutely right.
 So I'm super happy that you bring that up, Daniela.
 We have had a couple of conversations on that.
 So we also see that that needs to come
 from a legal and compliance perspective.
 That's not something we will have an opinion about.
 Great.
 But then Sarai is our ill vice, the continued intersection
 point to make sure that we get those perspective.
 Because we have a great opportunity to be clear on what
 that means in the AAC, for everyone to know exactly what
 those requirements are.
 So we can educate everyone coming to the AAC as well.
 So that in the end, everyone can have an opinion.
 Because it should not only be lawyers having an opinion.
 We need to educate us.
 Yeah, now exactly.
 Nobody, I mean, as a starting point.
 And we already have a few of those included
 in the questionnaire that Arthur has worked with VGM
 Federico.
 So we could make sure to align on that, then, Ilva,
 together with Arthur and that's great.
 Yes.
 We have one minute left.
 Actually, do we need--
 Sorry.
 No, I was just checking my sound.
 Yep.
 OK.
 One of the singers, so just to finalize.
 Actually, there was-- I had raised in this article.
 We had the other day for the work stream
 that we had an initiative that they,
 from their perspective, would take in three months
 to get through the review.
 So I promised to do a bit of a detail and double-click on that.
 But I will include the VG Federico author into that.
 But because we didn't have time to discuss that now,
 but that was something I wanted to bring up.
 So we just need to look at that, probably.
 So no time to update on the other initiatives today,
 but we can do that next week.
 And then we will have a pause on the working
 group for three weeks during summer vacations.
 OK.
 Thanks for today.
 Good discussions.
 Thank you.
