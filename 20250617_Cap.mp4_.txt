 We just have a bit of a delay to get access the room right and we'll start the meeting very soon. Thanks for joining.
 Okay, okay. <PERSON> is waiting for me to pick up.
 No, no, it's okay. I mean, one is just coming to pick us up. So we will start the meeting very shortly in the room.
 Okay. Okay. Sorry. I just saw your message. Is that the <PERSON>?
 I'm with <PERSON>. Yes. Right. So this is <PERSON> from from Captain and I am guessing, right <PERSON>?
 Yeah. Okay. Okay. Okay. Okay.
 And we're going to access the building. So you're going to you're you're walking to the meeting room. Am I right, <PERSON>?
 Yes. I'm what? Yeah. All right. Super. I'm with <PERSON>.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 We are on our way to the to the room. I'm not worried. Thanks for your patience.
 Understood. We're hanging on.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 Okay.
 So, so that the place to look into the model is discovered from from both sides and then really this piece is being compared.
 Is that something that the product X does to recover these links?
 Okay.
 Basically based on a large tree structure coming from the overall product and then different differentiating between different features just down to a specific function with a specific input and output.
 Okay.
 But when you wanted to.
 I think it's why we're interested in that.
 You said that you mentioned the system check here so it's really nice to be here actually a couple of questions.
 Perfect.
 Go on.
 Yes, please.
 If we take the meta model for us, the core function, so whether it's this slide or this slide, right?
 You were shared aside.
 Of course.
 Yes.
 And so the key piece that we function focused on was four key pillars across it.
 So product optimizer is is how we're taking those inputs that you mentioned, which can also include depending on the scale.
 Of what we're trying to capture.
 It can.
 It can pull in.
 NPS scores.
 It can pull in customer satin response tickets for those specific.
 So how many.
 L2 to L3 support escalations are doing.
 Right.
 It takes into any of the inputs.
 Detectualize or other for the environment.
 Right.
 So as we look at product optimizer and it's helping us capture.
 The key areas that have the lowest barrier of entry to capture those efficiencies around the development.
 Can you give example that.
 Yes, concrete use of this.
 Sure.
 So.
 We used to.
 When we come in and do a portfolio analysis, right?
 And what's here today.
 It's going to be a very manual process of us interviewing product donors and.
 And going through not just the code comers, right?
 But now we basically can take any of that.
 We can use pro generation to interview the product donors and the consumers of it.
 The developers themselves to better understand what the.
 They're developed developer experience.
 You're talking about the product optimizer.
 Correct.
 Right.
 So not just those technical inputs for all of the contextual pieces of.
 The teams are running that the agile processes are, right?
 So that we get that complete view of the.
 The dev setups.
 Landscape, right?
 Is it an introduction?
 It is yes.
 How was the satisfaction right from the user?
 Okay.
 It makes sense that the recommendation.
 It does.
 I think in a lot of cases, right?
 For example, we had a customer who is convinced that the biggest dependency was that a poor UX.
 Right.
 And so they were convinced that the next thing that they're going to start on the phase.
 Of their investment right was rewrite the UX.
 As we went through the optimizer piece, it turned out that no one actually cared about the UX because everything else fundamental.
 I was broken around their death process and.
 It says, okay, well, we can get to the UX.
 But first you have to go through all these other rewinds of the tool chain so that the developers have access to the tool sets that they need to.
 So it's a basic example.
 But an example of how we're re prioritizing.
 Where do we start?
 How do we?
 How do we gain the greatest level of efficiencies?
 Let's impact on the development teams.
 Right.
 And a more meaningful way.
 What what happens if the product has to be like a cyber physical system?
 No, you have these.
 You know.
 A vehicle system with many pieces and.
 And the feedback is, you know, attributing which part of the system this feedback is even related to any thoughts about how you would approach that.
 Yes.
 You want to counter.
 So we should say hi on the party.
 You know.
 Yes, yes, yes.
 Okay.
 So we can come back to.
 I think we have a couple sides here.
 Right.
 But for printables of where we are, right?
 So product optimizer.
 What's the as is product creator?
 To be to be honest, when we started building that agent, right?
 The core focus was rapid application development for MPs, right?
 So the customers that had.
 10, 12, 20 use cases that they were all seeking investment.
 You know, leadership in order to let's do this, right?
 And so we were hoping to be able to unlock faster MPs and use case validation ROI of these use cases for customers.
 So that we can get build and scale.
 The interesting thing is it is a great use case.
 You take a scoping document five pages or 500 pages.
 And we're using part creator to build from that move or from scratch the memory code basis into production.
 And around the test cases around us.
 The way that we have this actually in production.
 The most.
 Is actually around.
 Two, three.
 Supposedulation.
 Right.
 So triaging L2.
 You're creating a scope.
 We're working out for fixed act by the picture fixes, etc.
 Reason probably creator to do the code creation for the fixed.
 And then the test validation of that.
 So.
 Just want to be transparent there.
 Like in this game, where is this production?
 Our customers using it where we see the greatest level.
 Probably creator was built for one thing and now we're seeing the greatest areas around almost L2, L3.
 You know, triage for that in the code generation and test.
 This is something that I happen to be interested in.
 So that's the brief question.
 Right.
 One thing is, I mean, so in the quick MVP creation.
 I mean, I understand that.
 But how much of of of of of reuse and integration across one repositories to.
 System information.
 That's not code.
 Right.
 So how have you been thinking about the user story generation?
 No, no, no, no, no.
 It's still core generation, right?
 But, but, but let's say that there's there's a dependency in, you know, it's.
 The dependencies has spread out across multiple multiple repositories.
 All the context is not funneled into one.
 Right.
 Okay, but you could say I can solve that using a monorepo.
 For instance, okay.
 I can I can buy that to some extent, but would still be.
 You know how you've been thinking about this.
 But then also all the extra context that's not in code.
 Right.
 Some of the specifications that, you know, you don't find them in code basis.
 Right.
 So how have you been thinking about this?
 I mean, I know you will point to the matter model, but if something.
 If we go, if we go a little bit deeper than that would be helpful.
 Right.
 Yeah.
 So that's basically the configuration of the agents, which will have access to either of your systems.
 Possibly to connect to gyra to artifact to read to get repositories.
 Of course, wherever you have the source information, you can configure respective access.
 To our agents fetching the relevant information and putting it to the vector database,
 which will support any AI direction.
 Okay, so the thing with this is that I'm what I'm wondering about is.
 I mean, if if the development happens to be scoped.
 And if it's about a particular application, right, this configuration and.
 And setting up the necessary context becomes manageable.
 Right.
 But then if the scope of of product creation grows, right?
 I mean, and you want to be able to create across.
 A wide squad of of the embedded system, right? So have you thought?
 I mean, do you have any thoughts on how you want to be able to?
 Because here the configuration.
 Is needs to be more fluid in some sense, right?
 I mean, like it should be and it should also be very context specific.
 If I want to develop something related to suspension system.
 I mean, the, you know, there should be sufficient flexibility to go and seek.
 Information about that particular scope.
 And use a great skating in the sense that the available input information is very broad.
 But each specific case should be supported in a very detailed manner.
 Well, in principle, that's the theoretical background of this vector database.
 That whatever input information you have, you can put it there.
 It can grow very large.
 And you know the mechanism of embedding.
 Yeah, we have you have a specific topic under under consideration.
 You go with this embedding with this vector into the vector database to fetch the the circumstances to fetch those relevant source information from where it has come originally.
 Right to support this specific AI question.
 That is the mechanism which is used by our product exchanges.
 Right.
 Okay. So you've been able to solve this with with with the right pipeline is what you're saying.
 That's the principle of a trivial event generation.
 Right.
 Okay.
 Let's take a time.
 Do we want to get into some of the embedded specific pieces here?
 Yeah, well, as far as I've understood, you have already had contact to our author's AI or embedded AI solutions.
 I wasn't there, but we have.
 Yes, yes, yes, yes.
 So you might have seen where in this change, we come from embedded software requirements over some design visualizations on some diagrams being generated.
 Then all tools are Eric Semel with the respective visualization generated.
 And finally also the secode of those software components and then test cases for the secode, et cetera.
 And basically maybe we are even in a similar situation here that this development of embedded AI and author's AI has been performed independent of product X and in the first place.
 And now we are currently transforming that to use product X and why?
 Because it was a let's say simple approach for embedded AI and also AI know really agentic approach.
 Just a good prompt engineering, let's say.
 And to transform that into the product X framework to use really this agentic approach with feedback loops, multiple viewpoints checking each other, et cetera.
 That will bring another big benefit for that approach.
 Sure, I have the autosupply.
 Yeah, so more detail than to how we're trying to generate an autosupply architecture.
 Difficult to read, but basically we go through a whole software development process from requirements architecture, detailed design, configuration.
 Only unit testing and integration testing.
 And we have some some indications really measured on real projects.
 What what we think is a good indication of savings in terms of effort.
 So this last I was well about how I've come up to this number.
 Sorry, which one?
 Okay.
 Yes.
 And there was a bit of experiments.
 So we had the same type of work costs around my team utilizing this and by a team who's doing traditional development.
 And there's another one of the optimizer.
 Yeah, did you also need a twist?
 My understanding was that we had a specific use case for this.
 And I don't think that it was a major because now we're talking more about the better day.
 I said.
 Sorry.
 I'm different.
 Okay.
 If I can next.
 I mean, so if I connect back to one of the early questions that I was asking, what I was essentially trying to understand was like, okay, so let's say that you use these seven steps.
 To create one one component, right?
 As a parallel step that's that's doing to create another one, right?
 And you have many of these.
 And then you would probably have another stage of this process where you would want to compose or reuse these different pieces, right?
 So have you has has has has has product X been used in that kind of of a setting?
 Or is it is it is it mainly focusing on on on on on relatively bounded applications?
 That's actually the role of the.
 I think it's product optimizer right, that we will create from a from a larger system.
 These small sub steps, which which need to be executed there.
 So that is built in in product X due to a principal divide and conquer here.
 Divide a large problem into very small sub pieces and launch a specific agent for the specific task only on these small sub pieces.
 But the composition of these of these.
 And the component.
 You have the individual results then back to integrate all these.
 And this is something that you've been able to do with this in this pipeline.
 It may be that.
 Yeah, and I think the underlying.
 I would call it almost the fact is the more complexity environment, the greater levels of efficiency.
 If we are two person, you know, application, consumer, the enterprise, right.
 The efficiency gains are normal, right, because it was relatively.
 If we're looking at the war complex, the greater the level of efficiency.
 See the question here is that instead of we have one of those seven steps that within the development flow we have, we might have a two.
 Two hundred three hundred.
 In fact, I would connect to the complexity question.
 It's precisely the complexity that we want to solve, right, because not not not necessarily isolated to team X or team Y, but but but to put a large systems as a whole.
 But at the same time, while the gains are, you know, can be dramatic, but at the same time, the, you know, you also expose the system to.
 To many, to many, many more failure modes, right.
 So it's also one of the reasons why I'm trying to understand like.
 Okay, so has product.
 I mean, I can understand this.
 This is a tough problem to solve, right?
 I mean, so, but have you has product X and has your thinking proceeded to the level where you're.
 It's it's it's it goes.
 To the, you know, the composition of of multiple systems and being able to.
 You know, do that kind of a process at scale.
 It does right. I think we're still early days.
 Right.
 And production perspective, right.
 We're also, you know, 18 months, and I'm considering it.
 It means what we're.
 Right.
 I think we're 10 minutes left for the meeting.
 I think we'll be going to conclude.
 The next step for this.
 I think there's a couple of things at least I'm interested here.
 So please feel free to act from you, the team here.
 And how big is the user at a group base that you already have the product X here.
 And then this is what we understand.
 It's what a capacity of the product X.
 And then regarding the matter data here, I think we probably have a few more technical questions here.
 Are there any particular model you think you trained your own models?
 So is it the only.
 I think we're seeing the chat to the team models.
 So what are the.
 Because we already have this also developed within GT.
 We have we are using different models.
 So we want to see that.
 She means matter models are not available.
 Yes.
 Right.
 Good answer.
 I'm actually worried.
 Interesting.
 It will be good that we can see at the long.
 How does this work?
 And one other thing that I would add to this is.
 Is the is the element of evaluation and observability, right?
 So because we we write a lot of evaluators to, you know, to check what the process and the outcomes of agent work.
 So we're also like to understand how agent X.
 Exactly.
 Like helps us.
 Right.
 Right.
 So how do you use the any of the commercial.
 It looks like you use the origin as the multi agent system.
 For the right model, the realization.
 Okay.
 For that.
 I see.
 Yes.
 It is a very interesting.
 Right.
 Absolutely.
 Yes.
 Well, I think I don't like watching the time in the technical part.
 And then the ecosystem check.
 I think this is why we valid use the case for us.
 And then I would like to understand where you are the maturity of this.
 So this would be a route that passively.
 Yes.
 Yes.
 Yes.
 Absolutely.
 Because I think if I, if I, if I, if I just refer what what menacing.
 We're the integration services of, of, of, of products is important to understand.
 Because we need to, we need to make it, but.
 With an already complex tool landscape and an already complex model landscape.
 So this is, this is one of the reasons why it's, it's very important for us to understand.
 What the integration service.
 Right.
 Right.
 The information would look as though depending on their maturity, what they want and because, you know, since they don't buy products as a food.
 Against the vice steps.
 And we can also emphasize on how we can actually start.
 Yes.
 I think it would be good.
 We understand this technical details to see if it, if some of the user cases is a particular interesting for us to actually get that piece.
 We, we, we, if we see the way they are working with that and what all is could be a good way for us to adapt.
 Now we see that how do we can set this up.
 The level would be the best way.
 And then if it's possible, maybe, you know, we show that that most of it can do some piloting some areas.
 Don't you test the messages with you or with the product there?
 Absolutely.
 And wouldn't it be possible to also have a little bit more.
 Was it your side of what is interfaced by the system, what you are really.
 Because that can take with that conversation.
 We can either do it on the next conversation.
 Or it can be a little bit of a smaller preparation session, so we also work with that.
 Right.
 Then we can prepare.
 Yes.
 I think it will be.
 I mean.
 Okay.
 I'm speaking on behalf of my team.
 I think maybe we'll be good to start after summer after summer vacation.
 I think it's only few weeks.
 Summer vacation.
 But then I know our A team don't take the summer with us.
 There's a reason for everyone to get some work done when it's quiet.
 And that's that.
 So.
 What I want to do to really.
 In which I will.
 I want to see what happens.
 I should.
 Absolutely.
 So.
 We do have a little bit more focus.
 And it will need a longer session.
 We can have that.
 And then based on this.
 We'll see what are the areas that we do.
 So potentially.
 Okay.
 So we can summarize your topics for today.
 We invite to a preparation.
 Other meeting before we can show them.
 The team has time to prepare.
 Yes.
 Yeah.
 We can get the agenda locked before that.
 When they're taking us to continue.
 And.
 Typically.
 We're seeing the most effective.
 And three to four hour workshop.
 Two, three specific topics that we can.
 You know, really dive into.
 I'm getting some of the code pieces of it.
 And also, I think I think in some sense.
 One thing that will also be interesting is to understand the pieces of.
 Of products because.
 I think it's.
 I think it's safe to say that the integration challenge is going to be tremendous.
 So it would be good to.
 And there's also one of the reasons why.
 Why we hasn't there to go on for the.
 Which pieces that comes from.
 From from a vendor because it's really tough to integrate it.
 Right.
 So, in that sense, if, if the decomposition of.
 Of products, it was more clear than we'd be, then we'd be better.
 Better place to do, you know, the judge, which piece is better than.
 So that would be that's, that's, it's quite essential.
 Thank you.
 I'm.
 This.
 I don't.
 I don't.
 This.
 I don't.
 I didn't expect.
 Please.
 So that we can.
 Not much of a fashion.
 And I opened this right.
 But we're working with Microsoft.
 We're working on.
 It was around product specifically.
 So I think we can dive more into how it's complemented to.
 And then we're wrapping the meeting up that.
 This conversation around what does the transition to.
 HM to develop that look like.
 I think.
 In combination with the other two chains and the ecosystem partners will be something.
 Early time.
 Are we working with Microsoft?
 You know, in order to help accelerate these outcomes.
 Right.
 Right.
 Right.
 But also important to understand if it compliments well with our philosophy for agent development.
 Right.
 That's.
 Is at the end of the day.
 This is a hundred-year-old company, which we are, you know, which we're in the middle of transforming business.
 So that's.
 We cannot underestimate.
 And the fees of that.
 Right.
 Right.
 So many.
 So we have.
 Someone online.
 No, that's something.
 And there's something on the chat as well.
 What is the framework that they are placing their agent development on?
 As far as the embedded software engineering goes.
 I know.
 A lot of multiple.
 Okay.
 Sorry.
 Let me.
 Let me.
 Let me.
 Let me.
 Let me focus just on the on the on the agentic orchestration step alone.
 We do a lot of this development ourselves.
 I think.
 I think.
 Yeah.
 We do a lot of this development ourselves.
 But in combination, we use an agent graph techniques.
 I mean, you would see this in line graph, for instance, but we use a different framework.
 But we do a lot of.
 Of workflow development by ourselves.
 But then we combine this with agent rough techniques.
 Okay.
 And that's for this for the agent part, right?
 Okay.
 Thank you.
 Sure.
 And as far as the evaluators consistency verification goes, I think.
 On the deep dive workshop, we can also look at what we've been doing with.
 Authors are your XML verification.
 So we are using it as a part of the consistency verification for gendered.
 So you can use it.
 It's a two step process, right?
 So you could actually generate the items, but then you also have an agent working on the verification of those.
 We are XML output.
 So that's an example of what we might be, you know, what we could look at as far as consistency verification goes.
 There are a number of other verified use cases that we have a verified agent use cases.
 We have in the pipe, but we've not.
 Obviously, we've not yet addressed all of those.
 So we are on the.
 Fat words compliance tech.
 Our benefit consistency verification kind of use cases as well.
 Right, this was this would be really interesting because I really like to understand like how you.
 Or whether you will get leverage along as large and and what configurations would you use this, etc.
 So that would be really.
 Yes.
 Um, also the other side of it is interest for us.
 So yeah.
 I couldn't look at the chat.
 If someone had a question was up to someone.
 Yeah.
 I think we can come back with the date.
 I think that I wasn't here.
 Maybe you should be trying to see that.
 Any software domain unless we'd like to join us.
 So we were, we were checking.
 I think it will be.
 You know, the end of the oldest.
 So around.
 We have.
 So I think the only day.
 So the work should just go to now.
 Down.
 Maybe so it could be something.
 Yes.
 That's probably not what we are.
 Yeah.
 It's usually.
 The work.
 We better come back as well.
 Did the workshop.
 We could take base in August.
 You know, just for when I was like.
 And say that.
 That would make sense.
 Because if you're able to, like, less topics, the phase that we could.
 Yeah.
 That's the summer.
 So today, I asked for a big time.
 So that we can ask for a smaller team.
 We want to.
 And the second.
 Talk sometimes.
 Yes.
 Thank you.
 Yes.
 So this now we need.
 The different.
 Let us.
 And the steps.
 And.
 Perfect.
 Well, thank you very much for.
 Thank you.
 That's welcoming us.
 Thank you.
 Thank you.
 Thank you.
 I hope it was comfortable to tell you what you had seen already.
 but we got paid us for the next agents.
 - Yeah, see, I think last time we only see individual cases.
 And then what you mentioned, now we started also moving things
 into the product X.
 - Things are getting faster.
 - Okay. - For a certain period of the industry.
 - Public team, are you working on product X here?
