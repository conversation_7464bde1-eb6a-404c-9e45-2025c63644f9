 I'm stepping down now, so <PERSON> is taking over, so I'm 50% yours right now.
 That's why I booked that.
 We'll date you on the latest things that we have at.
 So, that's good.
 This is a background that you have.
 Yeah, it seems like I'm in office.
 I am at the office.
 Looks strange because sometimes I'm floating in the air.
 But are you in office already at home today?
 Yeah, no, no, no.
 The office, you know, we have to.
 Yeah, that's true.
 But I'm really at home today, so.
 I can prove it, you see.
 It's moving behind me.
 No, I'm a bit more often at the office.
 That's what is what is what is wanted.
 Well, I would say.
 But yeah, time to time at home because it's convenient, you know,
 to be on the can side when there is nobody.
 And I mean, sometimes you have even things that you need to do at home.
 You know, someone that's repair something or delivering something or whatever,
 or some medical appointments as well.
 Exactly.
 And that's why I'm home today.
 Normally, I'm also in office.
 So, yeah.
 Yeah.
 And we don't have the problem of not enough space in Leo.
 Officially.
 Officially.
 Officially.
 Officially, because I can tell you on Tuesday and Thursday when it is not school vacation.
 It's a bit difficult to find a place.
 Okay.
 That's not.
 Yeah.
 But officially, in terms of years, we have enough place sitting in place.
 Yeah, all you know.
 Yeah.
 So I wanted to talk a little bit with you and have an update on my area.
 And also, um, discuss how you could help you directly or indirectly.
 Yeah, I would say.
 So you remember two, two different areas, one that is vehicle software,
 the small business layer for vehicle software to change.
 And one area that is more service development in the energy consumption area.
 Welcome back with it.
 So first, um, yeah, vehicle software.
 Now, I have a GPU.
 It's very recent, uh, from first of April.
 Someone that you know, maybe, uh, KK in, in Bangalore, Krishna Kumar.
 Yeah.
 Yeah.
 Yeah.
 Yeah, it was just before, um, uh, project manager and he was working the VC context for several years.
 So now he has, he has joined.
 So we are building a team or we have started to build a team.
 Officially, I have a tech lead, um, software engineer that is more junior and this GPU.
 But it's you know, it's up and down.
 So, um, we have had to release, um, the tech lead.
 For different reason, different reason, uh, one being that he wanted to have.
 A more stable stable team.
 Okay.
 We didn't have a GPU.
 It was quite difficult to be involved with CPS of fine diagnostic for values reason.
 And yeah, he said, okay, I would like to have a real, um, you know,
 team that is working with, you know, input coming from the business.
 Yeah.
 And not only investigation because that was mostly what we were doing.
 Sorry.
 So we had to release the guy.
 Now we are working on, um, something called mismatch and, um, an auto resolution.
 Uh, you are, you know, little bit the, the, the context of, um,
 vehicle software deployments.
 So download.
 Uh, enough for a market.
 So we have, we have NAMS.
 Uh, that is under NAMS is a solution on the by space of fine diagnostic.
 And then we have technical and GDSP on the other side from a dealer perspective.
 So.
 And, and we have quite big issue.
 Uh, we have gaps in terms of, um, truck.
 State, let's say, truck, um, definition between what we have in VA,
 what is normally the official, um, configuration of the truck.
 And what is the reality?
 Both physically and from a software perspective.
 And that's drive to some, um, that's what we call mismatch.
 And that drives some, um, issue in terms of deployment.
 Because GD, actual and GDSP.
 They are checking GDA, checking the, the trucks itself.
 And sometime there is a gap.
 And then it blocked the software grade.
 Okay.
 And it's quite a huge number.
 It's a problem for the dealer.
 It's a problem for the customer.
 And it's a problem for us as well.
 So, so.
 So the idea is that we have a mismatch analysis as a unique service.
 There are some mismatch analysis a little bit everywhere.
 In fact, all in GDSP in NAMS, but nothing consolidated as a service.
 So we have one unique service for mismatch analysis.
 And then there is this auto resolution service that aim to correct the gaps.
 Oh, especially correct in GD and make an update automatically in VD.
 And, um, and if we cannot correct it, we shouldn't have the possibility to give
 it to solve the problem to the dealer.
 Okay.
 So three steps, um, service.
 So we are just at the beginning.
 There were some, yeah, some values.
 That's a reflection from GDSP from CPA from GTT.
 So now we have one track.
 We know that it's important.
 We need to have something that is centrally defined.
 One unique service.
 And we are investigating the business rules that we need to put in place.
 So that's where we are.
 It's purely CPS after a diagnostic.
 This is considered as a core functionality.
 Okay.
 So we will not be responsible of that on the long run.
 We work on it.
 And actually KK is has become the AP corner for that.
 And we aim to define the rules in two cadents.
 And I have a start of development somewhere during summer.
 Okay.
 Molly.
 Yes.
 Um.
 Now, uh, in parallel, the idea was to have an exploration of the smart business layer.
 What kind of smart services we will develop in the future.
 This is a little bit down.
 So, uh, this is where we would like to ask you for.
 We need to have something to do for the team.
 Otherwise.
 You stop everything.
 And we close the team.
 That's important.
 So there are discussion on that.
 And this is where I would like to ask you for.
 For support into into bracket.
 How we could get help from architect.
 In the end, the context.
 To make and to conduct this.
 This investigation, this exploration.
 There are many architect that are involved.
 From GTT standpoint, of course, from CPS of time diagnostic from GDSP.
 As part of the mismatch and authorization, but also for.
 That will be important for the for the exploration of on the on the smart business.
 And.
 But no one have really.
 The willingness and the time to do that.
 Okay.
 You know, there is, of course, T3.
 There is the modernization of the SVD.
 And this is mostly the most important.
 And we can understand it's not.
 It's not an issue.
 But at the end of the day, we need to move forward with this team.
 And I'm pushing for that.
 Yeah.
 So that was the first thing.
 And I wanted you to reflect on that.
 For the time being, I don't think we need.
 Support.
 So it's we are still working with the same team in GTT.
 You remember a year ago, we had to give everything to.
 You remember that.
 So they are finalized.
 They have finalized the development of the API energy API for a wrong general.
 And yes, it's in production.
 It's not yet consumed by years.
 It will be so.
 So, they have moved for one.
 It's in.
 Normally, they are not start approved.
 Normally, I have not followed an unchecked.
 But that was the goal.
 I mean, everything is in Azure.
 And.
 Normally, everything should be fine.
 But they are isolated.
 It's in GTT.
 It's.
 GTT managed solution, which is not the perfect way in my view.
 And we have discussion with with GTT.
 To change that.
 But that will not be for tomorrow.
 What we have been doing in the meantime.
 What we have been doing.
 And actually, it's, it's a more advanced algorithm.
 That is aiming to optimize a complete, a complete mission.
 I have a slide that I can show.
 Maybe.
 This is something I share.
 I will share my screen.
 Okay, like this.
 This is a slide that I have shown in BRM.
 A long time back several months ago.
 This one.
 Should work.
 No, not this one.
 Sorry.
 I will put it in slideshow mode instead.
 So, the other screen.
 This one.
 And I need to move the video screen.
 So mission management service.
 Of course, we'll use energy API to calculate the range.
 And the energy consumption.
 It's on top of it.
 And I will maybe.
 With everything.
 This is for drivers and.
 Feet owner.
 Feet managers.
 Actually, that's.
 That's.
 As a user.
 So it will take, of course, the route.
 Into account, the routes.
 The vehicle parameters, like what we have in energy.
 But also the driver breaks the weather, the traffic.
 So where the truck will charge or uncharged goods.
 And the charging station.
 The idea is to have an optimization engine that will optimize the full.
 Thing from the starting point to the end point.
 And tell the driver or the fleet owner, the fleet manager.
 Where it will be best for the driver to rest.
 The right will be best to charge.
 Including the cost of the charging.
 Including the cost of the charging.
 Because.
 I mean, depending of the.
 The complete mission.
 Then it's not necessary to be fully charged all the time.
 And you can maybe wait a little bit and charge a little bit.
 More at the certain point of time, or less.
 And eventually in order to optimize the full, the full mission, the full trip.
 So it's, it's a little bit more intelligent that than the, the, the simple route and range that we have today.
 And we have, you know, some of the things that we have, you know, we have a, we have, you know, some of the things that we have, you know, some of the things that we have, you know, some of the things that we have, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today, you know, some of the things that we have today.
 So let's see it's ongoing, but the idea and that was presented last week.
 We have a tech committee, now it is, that was the first one last week.
 And the idea from VEP, this is the new name of the team in GTT, before it was them.
 You remember, the energy management, now it's that VEP efficiency and productivity.
 There is a new, a new GTM, a new head coming up from outside the level.
 And I, and I, and I, and I, and I, and I, and I, but I'm not sure.
 And I, under the steel part of the team, but he's not not anymore the manager and the GTM is strategy leader for, for the team.
 And, and we have a product owner that is so up for this mission management, charge planner, it is called nowadays.
 And that presents the goal to have, you know, that there will be several APIs for that as well.
 We will be consuming energy API to calculate the range and the energy consumption.
 But on, on the top, there will be a kind of orchestrator that will call the value CPIs and make the consolidation call the optimization engine and send back the optimized track.
 The idea is, or the idea is that we replace the wrapper in energy API with that.
 Okay. Okay. So, of course, that's create some concerns in immob.
 But there are some changes. Apparently, they are lying on the way to, on the list. So let's see. So our goal is to work on that.
 We have made already a first release of that during April, beginning of April.
 So, GTT, they are able to use the, the service.
 Online, in a way, in a computer on a laptop, in a truck.
 Okay. In a test truck. They are able to, you know, to run the optimization engine and compare with the real value of the test truck.
 So they can rework and optimize their optimization engine.
 So we are at the first step. And the idea now is to prepare the industrialization as well prepare an integration with an external partner.
 I told you, this is for the driver and fleet managers. So the idea is to have the service consume or available in the truck at a certain point of time.
 Not as it's not, it's not an embedded software. It will be something, a kind of app, a variable in the central stream.
 It should be available for fleet manager. And this is where we have, or GTT has started to work with a partner that is providing services for transport management system, you know, TMS.
 The ERP used by logistic companies. So the idea is through this company, we will provide the service that will be available in some TMS, transport management system.
 So they can fleet manager can prepare a mission for a particular driver and truck in advance.
 And based on certain importance is, and then the morning, the driver will come, okay, he has his plan with an optimized road.
 Of course, you never know what will happen on the road. It can be an accident, something need to change, because there is a delay to unload some goods, for instance.
 You need to run again the optimization engine, so you will be able to do it, either from the truck, either from with an orange, orange and orange.
 Okay, so that's the idea, the concept.
 So very interesting, very interesting, because now we have a target date to make it available with the truck.
 Week, 36, 37.
 With the long-range Bev, really sorry, SOP here.
 So, so start to ask, you know, the pressure of the product plan.
 So, it's really interesting here.
 So, let's see, the remaining thing is this conflict, I would say, with imab.
 Let's see, I mean, this is of course concerning for the plan of and mohali, because they have experience that already last year.
 It was not a good experience, I can tell you, and the off course concept, we have had discussion in the past weeks.
 But apparently, the strategy for GTT is to go with us responsible of the off-born part of the development.
 And one thing, the new manager, this Anaik, that he told me that a couple of weeks back, you know, I don't want to be called on the Sunday, because there is a break something in the service.
 Yeah, that's our stuff, right?
 Yeah, stuff.
 So, we can manage that, and we can secure a team for that.
 It's not only the development, it will be how we manage it on the daily base, when it will be in production.
 So, yeah, very interesting, and for the time being, no need for you to be involved, but it's more the vehicle software.
 And more, the exploration.
 There is enough people involved for mismatch and auto-resolution, but more for the exploration.
 Do you have a certain architect in that area from our end?
 Nope, nope.
 From our end, no one.
 And, you know, the difficulty in this is to create a new team, while we don't have the account.
 So, we need to get the account from here and there.
 And to get also the financing.
 And then we are, we have difficulty with the financing.
 Everything is managed by CPS of fine diagnostic as of now.
 We don't have our own budget.
 There is a part dedicated to smart business layer.
 And for the timing, it's a little bit downbaritized by GTT.
 Okay, okay.
 They want first to have this mismatch and auto-resolution.
 But at the end, at the same time, we need to create the space for the team on the long run.
 Not only to work 100% on the mismatch and auto-resolution.
 So that's the things I'm discussing with Bosnayaki and Shondayka.
 Okay.
 Yeah, good, good.
 And we have a based touch-based with Lina on a regular basis on that.
 She was, you know, she was in Bangalore last week.
 You know that?
 Yeah, I've heard that.
 So, normally, she has touch-based with Shondayka.
 I don't have the results yet.
 But, yeah.
 And we have been discussing to involve you as well in the reflection.
 Perfect, perfect.
 I normally talk to Chandrika as well.
 So, that's good.
 I have that contract.
 Yeah.
 It's good that we have that both both are you and I.
 So we have a good contact there.
 And let's see.
 Let's do like this.
 I will digest this a little bit.
 Yeah.
 And then it's very good that you inform me, Philippa.
 I really like that.
 And also I can also just have an update from Morali as well as I know what they are doing.
 Yeah.
 Yeah.
 Yeah.
 Because there are quite advanced now on the proposal for Mission Management Charge Planner.
 Now, the question to be ready to replace the wrapper is coming on the table.
 It's just you.
 Yeah.
 We were feeling that's the past week.
 But now it's really formal.
 It's presented as some of the mail this morning to to Pranav.
 So let's investigate that.
 I think it will be interesting.
 Hope that we will not be these have this conflict again with him up.
 They need to focus on the algorithm.
 We need to take the IT part.
 So let's see how this revolve.
 I agree.
 I agree.
 Good.
 Yeah.
 Thank you for this.
 It's really good.
 And you have been busy.
 What I can see.
 Yeah.
 Hi, I'm a say it's it's.
 I mean, you make two step for one one step back.
 One step for one to step back.
 And I have been telling Lena a couple of time.
 Lena, I'm tired with that.
 So on both things, you know, on both subjects.
 Now we have to decide.
 Is there something to take everything for the vehicle software?
 So now we have kind of way forward.
 Let's see.
 And on the same for for a GT in the energy management side.
 If we cannot create the team, the space for the team, then we have to decide.
 It's not a problem.
 If they decide that team up will do everything.
 We will do something else.
 And you don't have or maybe you don't know.
 Or maybe you have some time discussion with us car.
 In the coffee corner.
 So they are working with this for the bus stuff.
 So so they have enough to do.
 There is one one more thing on the not on mission management.
 Oh, yeah, sorry on mission management.
 One more thing that is coming on the table.
 We are asked to create a service for that will be.
 Used by initial management or on general.
 It's the rolling resistance and air drag.
 You remember we were talking about that already in LHP.
 Nothing has happened.
 There is something existing for holding resistance.
 It's included in EDB.
 You know that?
 Okay.
 So apparently on EDB side, I have not talked directly with them yet.
 But apparently they don't have the bandwidth to do anything.
 So they will be happy to.
 Take it over or to to trans.
 So the idea is that from GTT VEP is that we take the responsibility to create an API for both of them.
 Okay.
 Not only for Bav actually also for any kind of engine.
 Yeah, then I understand.
 So and we'll be one one other component there.
 Perfect.
 So you know.
 Then I have a little bit of thinking and.
 Yeah, let's let's reconnect in a couple of weeks.
 Maybe not next week, the week after being off the week after.
 Yeah.
 And I will try to ignore your management meeting as well.
 Oh, yeah.
 So so I get up to speed there as well.
 Yes, excellent.
 Then we will have opportunity to talk.
 But I will schedule a short discussion then into it.
 So we can.
 After your reflection, we can we can.
 Oh, what I have done now.
 Okay, you see my wonderful background.
 Yeah.
 Okay, then let's let's.
 You reflect on your side and we talk again into it.
 We will do that.
 Thank you very much.
 Thank you very much.
 Thank you very much.
